{"version": "0.0.663", "lastUpdated": "2025-08-02T20:37:41.141Z", "libraries": {"ui": {"components": {"FinActionsMenuComponent": {"metadata": {"name": "FinActionsMenuComponent", "selector": "fin-actions-menu", "modulePath": "@fincloud/ui/actions-menu", "exportPath": "@fincloud/ui/actions-menu", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/actions-menu/src/lib/components/action-menu/actions-menu.component.ts", "dependencies": ["@fincloud/utils/angular-material"], "documentation": "", "category": "navigation", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-actions-menu", "inputs": [{"name": "yPosition", "type": "MenuPositionY", "required": true, "defaultValue": "below", "documentation": "", "examples": [], "deprecated": false}, {"name": "xPosition", "type": "MenuPositionX", "required": true, "defaultValue": "before", "documentation": "", "examples": [], "deprecated": false}, {"name": "class", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/utils/angular-material"], "dependents": ["FinBreadcrumbComponent", "FinSplitButtonComponent"], "relatedComponents": ["FinAvatarDefaultComponent", "FinAvatarParticipantsComponent", "FinBreadcrumbComponent", "FinMenuItemComponent", "FinTreeMenuComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinAIShadowEffectComponent": {"metadata": {"name": "FinAIShadowEffectComponent", "selector": "[finAiShadowEffect]", "modulePath": "@fincloud/ui/ai-shadow-effect", "exportPath": "@fincloud/ui/ai-shadow-effect", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/ai-shadow-effect/src/lib/components/ai-shadow-effect.component.ts", "dependencies": [], "documentation": "", "category": "other", "tags": [], "deprecated": false}, "api": {"selector": "[finAiShadowEffect]", "inputs": [{"name": "finAiShadowEffect", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": [], "dependents": [], "relatedComponents": ["FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent", "FinBadgeIndicatorComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinAiSuggestionComponent": {"metadata": {"name": "FinAiSuggestionComponent", "selector": "fin-ai-suggestion", "modulePath": "@fincloud/ui/ai-suggestion", "exportPath": "@fincloud/ui/ai-suggestion", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/ai-suggestion/src/lib/components/ai-suggestion/ai-suggestion.component.ts", "dependencies": ["@fincloud/utils/control-value-accessor"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-ai-suggestion", "inputs": [{"name": "ai<PERSON>nabled", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "typewriterSpeed", "type": "any", "required": true, "defaultValue": 50, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "aiSuggestionReady", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngOnInit", "signature": "ngOnInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "ngOnChanges", "signature": "ngOnChanges(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/utils/control-value-accessor"], "dependents": ["FinDatePickerComponent", "FinDropdownComponent", "FinInputComponent", "FinTextAreaComponent"], "relatedComponents": ["FinAIShadowEffectComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent", "FinBadgeIndicatorComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinBorderSweepComponent": {"metadata": {"name": "FinBorderSweepComponent", "selector": "fin-border-sweep", "modulePath": "@fincloud/ui/animations", "exportPath": "@fincloud/ui/animations", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/animations/src/lib/components/border-sweep/border-sweep.component.ts", "dependencies": ["@fincloud/utils/functions"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-border-sweep", "inputs": [{"name": "enableAnimation", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "borderRadius", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "borderWidth", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "animationDelay", "type": "any", "required": true, "defaultValue": 0, "documentation": "", "examples": [], "deprecated": false}, {"name": "animationDuration", "type": "any", "required": true, "defaultValue": 1.5, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/utils/functions"], "dependents": ["FinDocumentClassificationComponent", "FinDocumentClassificationLoadingComponent"], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBadgeAppComponent", "FinBadgeIconComponent", "FinBadgeIndicatorComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinAvatarDefaultComponent": {"metadata": {"name": "FinAvatarDefaultComponent", "selector": "fin-avatar-default", "modulePath": "@fincloud/ui/avatar-default", "exportPath": "@fincloud/ui/avatar-default", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/avatar-default/src/lib/components/avatar-default/avatar-default.component.ts", "dependencies": ["@fincloud/ui/types"], "documentation": "", "category": "navigation", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-avatar-default", "inputs": [{"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "image", "type": "string", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/types"], "dependents": ["FinDropdownComponent"], "relatedComponents": ["FinActionsMenuComponent", "FinAvatarParticipantsComponent", "FinBreadcrumbComponent", "FinMenuItemComponent", "FinTreeMenuComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinAvatarParticipantsComponent": {"metadata": {"name": "FinAvatarParticipantsComponent", "selector": "fin-avatar-participants", "modulePath": "@fincloud/ui/avatar-participants", "exportPath": "@fincloud/ui/avatar-participants", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/avatar-participants/src/lib/components/avatar-participants/avatar-participants.component.ts", "dependencies": [], "documentation": "", "category": "navigation", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-avatar-participants", "inputs": [{"name": "participantType", "type": "FinParticipantType", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "showType", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": [], "dependents": [], "relatedComponents": ["FinActionsMenuComponent", "FinAvatarDefaultComponent", "FinBreadcrumbComponent", "FinMenuItemComponent", "FinTreeMenuComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinBadgeAppComponent": {"metadata": {"name": "FinBadgeAppComponent", "selector": "fin-badge-app", "modulePath": "@fincloud/ui/badges", "exportPath": "@fincloud/ui/badges", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/badges/src/lib/components/badge-app/badge-app.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-badge-app", "inputs": [{"name": "type", "type": "FinBadgeAppType", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "status", "type": "FinBadgeAppStatus", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types"], "dependents": ["FinFilterTabsComponent", "FinSearchComponent", "FinTabsComponent"], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeIconComponent", "FinBadgeIndicatorComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinBadgeIconComponent": {"metadata": {"name": "FinBadgeIconComponent", "selector": "fin-badge-icon", "modulePath": "@fincloud/ui/badges", "exportPath": "@fincloud/ui/badges", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/badges/src/lib/components/badge-icon/badge-icon.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-badge-icon", "inputs": [{"name": "type", "type": "FinBadgeType.ACTIVE | FinBadgeType.DEFAULT | FinBadgeType.ATTENTION | FinBadgeType.INACTIVE | FinBadgeType.ELLIPSE", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "name", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "src", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIndicatorComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinBadgeIndicatorComponent": {"metadata": {"name": "FinBadgeIndicatorComponent", "selector": "fin-badge-indicator", "modulePath": "@fincloud/ui/badges", "exportPath": "@fincloud/ui/badges", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/badges/src/lib/components/badge-indicator/badge-indicator.component.ts", "dependencies": ["@fincloud/ui/types"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-badge-indicator", "inputs": [{"name": "count", "type": "any", "required": true, "defaultValue": 0, "documentation": "", "examples": [], "deprecated": false}, {"name": "type", "type": "FinBadgeType", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "showMaxCount", "type": "number", "required": true, "documentation": "Use `maxCount` instead.", "examples": [], "deprecated": false}, {"name": "maxCount", "type": "number", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/types"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinBadgeInvitationComponent": {"metadata": {"name": "FinBadgeInvitationComponent", "selector": "fin-badge-invitation", "modulePath": "@fincloud/ui/badges", "exportPath": "@fincloud/ui/badges", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/badges/src/lib/components/badge-invitation/badge-invitation.component.ts", "dependencies": [], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-badge-invitation", "inputs": [{"name": "type", "type": "FinBadgeCustomerStatus", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": [], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinBadgeStatusComponent": {"metadata": {"name": "FinBadgeStatusComponent", "selector": "fin-badge-status", "modulePath": "@fincloud/ui/badges", "exportPath": "@fincloud/ui/badges", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/badges/src/lib/components/badge-status/badge-status.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-badge-status", "inputs": [{"name": "type", "type": "FinBadgeStatus", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "text", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "iconName", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "iconSrc", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinBreadcrumbComponent": {"metadata": {"name": "FinBreadcrumbComponent", "selector": "fin-breadcrumb", "modulePath": "@fincloud/ui/breadcrumb", "exportPath": "@fincloud/ui/breadcrumb", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/breadcrumb/src/lib/components/breadcrumb/breadcrumb.component.ts", "dependencies": ["@fincloud/ui/actions-menu", "@fincloud/ui/button", "@fincloud/ui/icon", "@fincloud/ui/menu-item", "@fincloud/ui/tooltip", "@fincloud/ui/truncate-text", "@fincloud/ui/types", "@fincloud/utils/functions"], "documentation": "", "category": "navigation", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-breadcrumb", "inputs": [{"name": "items", "type": "FinBreadcrumbItem[]", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "backButtonClicked", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "breadcrumbClicked", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngAfterViewInit", "signature": "ngAfterViewInit(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}, {"name": "ngOnChanges", "signature": "ngOnChanges(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}, {"name": "ngOnDestroy", "signature": "ngOnDestroy(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/actions-menu", "@fincloud/ui/button", "@fincloud/ui/icon", "@fincloud/ui/menu-item", "@fincloud/ui/tooltip", "@fincloud/ui/truncate-text", "@fincloud/ui/types", "@fincloud/utils/functions"], "dependents": [], "relatedComponents": ["FinActionsMenuComponent", "FinAvatarDefaultComponent", "FinAvatarParticipantsComponent", "FinMenuItemComponent", "FinTreeMenuComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinButtonComponent": {"metadata": {"name": "FinButtonComponent", "selector": "", "modulePath": "@fincloud/ui/button", "exportPath": "@fincloud/ui/button", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/button/src/lib/components/button/button.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/angular-material"], "documentation": "", "category": "action", "tags": [], "deprecated": false}, "api": {"selector": "", "inputs": [{"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "shape", "type": "FinButtonShape", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "appearance", "type": "FinButtonAppearance", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "isActive", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "attention", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "elevation", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/angular-material"], "dependents": ["FinBreadcrumbComponent", "FinDatePickerComponent", "FinDocumentClassificationComponent", "FinDocumentClassificationLoadingComponent", "FinDropdownComponent", "FinDropdownChipsComponent", "FinBasicUsageComponent", "FinConfirmationComponent", "FinFullSizeComponent", "FinLayoutComponent", "FinModalConfigsComponent", "FinSlotExampleComponent", "FinCompactPaginatorComponent", "FinBasicComponent"], "relatedComponents": ["FinButtonActionComponent", "FinButtonFabComponent", "FinButtonLinkComponent", "FinRadioComponent", "FinSplitButtonComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinButtonActionComponent": {"metadata": {"name": "FinButtonActionComponent", "selector": "button[fin-button-action]", "modulePath": "@fincloud/ui/button", "exportPath": "@fincloud/ui/button", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/button/src/lib/components/button-action/button-action.component.ts", "dependencies": ["@fincloud/ui/types"], "documentation": "", "category": "action", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "button[fin-button-action]", "inputs": [{"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "shape", "type": "FinButtonShape", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "actionType", "type": "FinButtonActionType", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/types"], "dependents": [], "relatedComponents": ["FinButtonComponent", "FinButtonFabComponent", "FinButtonLinkComponent", "FinRadioComponent", "FinSplitButtonComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinButtonFabComponent": {"metadata": {"name": "FinButtonFabComponent", "selector": "button[fin-button-fab]", "modulePath": "@fincloud/ui/button", "exportPath": "@fincloud/ui/button", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/button/src/lib/components/button-fab/button-fab.component.ts", "dependencies": ["@fincloud/ui/types"], "documentation": "", "category": "action", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "button[fin-button-fab]", "inputs": [{"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/types"], "dependents": [], "relatedComponents": ["FinButtonComponent", "FinButtonActionComponent", "FinButtonLinkComponent", "FinRadioComponent", "FinSplitButtonComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinButtonLinkComponent": {"metadata": {"name": "FinButtonLinkComponent", "selector": "", "modulePath": "@fincloud/ui/button", "exportPath": "@fincloud/ui/button", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/button/src/lib/components/button-link/button-link.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/utils/angular-material"], "documentation": "", "category": "action", "tags": [], "deprecated": false}, "api": {"selector": "", "inputs": [], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/utils/angular-material"], "dependents": [], "relatedComponents": ["FinButtonComponent", "FinButtonActionComponent", "FinButtonFabComponent", "FinRadioComponent", "FinSplitButtonComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinDotsLoaderComponent": {"metadata": {"name": "FinDotsLoaderComponent", "selector": "fin-utils-dots-loader", "modulePath": "@fincloud/ui/button", "exportPath": "@fincloud/ui/button", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/button/src/lib/components/dots-loader/dots-loader.component.ts", "dependencies": ["@fincloud/ui/types"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-utils-dots-loader", "inputs": [{"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/types"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinCardLabelComponent": {"metadata": {"name": "FinCardLabelComponent", "selector": "fin-card-label", "modulePath": "@fincloud/ui/card-label", "exportPath": "@fincloud/ui/card-label", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/card-label/src/lib/components/card-label/card-label.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/separators", "@fincloud/ui/truncate-text", "@fincloud/ui/types"], "documentation": "", "category": "layout", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-card-label", "inputs": [{"name": "label", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "iconName", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "iconSrc", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/separators", "@fincloud/ui/truncate-text", "@fincloud/ui/types"], "dependents": [], "relatedComponents": ["FinExpansionPanelComponent", "FinSidePanelComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinAreaChartComponent": {"metadata": {"name": "FinAreaChartComponent", "selector": "fin-area-chart", "modulePath": "@fincloud/ui/charts", "exportPath": "@fincloud/ui/charts", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/charts/src/lib/components/area-chart/area-chart.component.ts", "dependencies": [], "documentation": "", "category": "data-display", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-area-chart", "inputs": [{"name": "labels", "type": "string[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "values", "type": "number[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "borderColor", "type": "any", "required": true, "defaultValue": "#00675D", "documentation": "", "examples": [], "deprecated": false}, {"name": "pointBackgroundColor", "type": "any", "required": true, "defaultValue": "#FFFFFF", "documentation": "", "examples": [], "deprecated": false}, {"name": "showGrid", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [{"name": "ngAfterViewInit", "signature": "ngAfterViewInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "ngOnDestroy", "signature": "ngOnDestroy(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": [], "dependents": [], "relatedComponents": ["FinBarChartComponent", "FinDoughnutChartComponent", "FinTableComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinBarChartComponent": {"metadata": {"name": "FinBarChartComponent", "selector": "fin-bar-chart", "modulePath": "@fincloud/ui/charts", "exportPath": "@fincloud/ui/charts", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/charts/src/lib/components/bar-chart/bar-chart.component.ts", "dependencies": ["@fincloud/ui/expansion-panel"], "documentation": "", "category": "data-display", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-bar-chart", "inputs": [{"name": "labels", "type": "string[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "bars", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "areBarsStacked", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "showGrid", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "autoColors", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "valuePrefix", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "valueSuffix", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [{"name": "ngAfterViewInit", "signature": "ngAfterViewInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "ngOnDestroy", "signature": "ngOnDestroy(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/expansion-panel"], "dependents": [], "relatedComponents": ["FinAreaChartComponent", "FinDoughnutChartComponent", "FinTableComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinDoughnutChartComponent": {"metadata": {"name": "FinDoughnutChartComponent", "selector": "fin-doughnut-chart", "modulePath": "@fincloud/ui/charts", "exportPath": "@fincloud/ui/charts", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/charts/src/lib/components/doughnut-chart/doughnut-chart.component.ts", "dependencies": ["@fincloud/ui/types", "@fincloud/utils/decorators"], "documentation": "", "category": "data-display", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-doughnut-chart", "inputs": [{"name": "labels", "type": "string[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "colors", "type": "string[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "autoColors", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "valuePrefix", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "valueSuffix", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "enabledTooltip", "type": "any", "required": true, "defaultValue": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "totalChartValue", "type": "any", "required": true, "defaultValue": 0, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [{"name": "ngAfterViewInit", "signature": "ngAfterViewInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "ngOnDestroy", "signature": "ngOnDestroy(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/types", "@fincloud/utils/decorators"], "dependents": [], "relatedComponents": ["FinAreaChartComponent", "FinBarChartComponent", "FinTableComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinCheckboxComponent": {"metadata": {"name": "FinCheckboxComponent", "selector": "fin-checkbox", "modulePath": "@fincloud/ui/checkbox", "exportPath": "@fincloud/ui/checkbox", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/checkbox/src/lib/components/checkbox.component.ts", "dependencies": ["@fincloud/ui/truncate-text", "@fincloud/ui/types", "@fincloud/utils/angular-material", "@fincloud/utils/control-value-accessor"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-checkbox", "inputs": [{"name": "label", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "labelPosition", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "changed", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "setIndeterminateState", "signature": "setIndeterminateState(value: boolean): void", "parameters": [{"name": "value", "type": "boolean", "optional": false}], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/truncate-text", "@fincloud/ui/types", "@fincloud/utils/angular-material", "@fincloud/utils/control-value-accessor"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinDatePickerComponent": {"metadata": {"name": "FinDatePickerComponent", "selector": "fin-date-picker", "modulePath": "@fincloud/ui/date-picker", "exportPath": "@fincloud/ui/date-picker", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/date-picker/src/lib/components/date-picker/date-picker.component.ts", "dependencies": ["@fincloud/ui/ai-suggestion", "@fincloud/ui/button", "@fincloud/ui/icon", "@fincloud/ui/truncate-text", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor", "@fincloud/utils/services"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-date-picker", "inputs": [{"name": "label", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "placeholder", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "selectionMode", "type": "FinDatePickerSelectionMode", "required": true, "defaultValue": "single", "documentation": "", "examples": [], "deprecated": false}, {"name": "view", "type": "FinDatePickerView", "required": true, "defaultValue": "date", "documentation": "", "examples": [], "deprecated": false}, {"name": "disabledDates", "type": "Date[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "showIcon", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "showButtonBar", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "dynamicErrorSpace", "type": "FinErrorSpace", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "readonly", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "externalFieldMessages", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "ai<PERSON>nabled", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "selectDate", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "closeCalendar", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "clearDate", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "inputChange", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "aiSuggestionReady", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngOnChanges", "signature": "ngOnChanges(changes: SimpleChanges): void", "parameters": [{"name": "changes", "type": "SimpleChanges", "optional": false}], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "ngOnInit", "signature": "ngOnInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "ngAfterViewInit", "signature": "ngAfterViewInit(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/ai-suggestion", "@fincloud/ui/button", "@fincloud/ui/icon", "@fincloud/ui/truncate-text", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor", "@fincloud/utils/services"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinInputDemoComponent": {"metadata": {"name": "FinInputDemoComponent", "selector": "fin-input-demo", "modulePath": "@fincloud/ui/demos", "exportPath": "@fincloud/ui/demos", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/demos/src/lib/components/input-demo/input-demo.component.ts", "dependencies": ["@fincloud/ui/input", "@fincloud/ui/separators", "@fincloud/ui/slide-toggle"], "documentation": "", "category": "form", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-input-demo", "inputs": [], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/input", "@fincloud/ui/separators", "@fincloud/ui/slide-toggle"], "dependents": [], "relatedComponents": ["FinFieldMessagesComponent", "FinInputComponent", "FinInputCountdownComponent", "FinInputProgressComponent", "FinStepperFieldComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinDirectoryComponent": {"metadata": {"name": "FinDirectoryComponent", "selector": "fin-directory", "modulePath": "@fincloud/ui/directory", "exportPath": "@fincloud/ui/directory", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/directory/src/lib/components/directory/directory.component.ts", "dependencies": ["@fincloud/ui/icon"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-directory", "inputs": [{"name": "selected", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "disabled", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinDocumentComponent": {"metadata": {"name": "FinDocumentComponent", "selector": "fin-document", "modulePath": "@fincloud/ui/document", "exportPath": "@fincloud/ui/document", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/document/src/lib/components/document/document.component.ts", "dependencies": ["@fincloud/ui/container", "@fincloud/ui/icon", "@fincloud/ui/loader", "@fincloud/ui/truncate-text"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-document", "inputs": [{"name": "disabled", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "warning", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "error", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "selected", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "label", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "description", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "dateAndTime", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "imageSrc", "type": "string | A<PERSON>y<PERSON>uffer", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/container", "@fincloud/ui/icon", "@fincloud/ui/loader", "@fincloud/ui/truncate-text"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinDocumentClassificationComponent": {"metadata": {"name": "FinDocumentClassificationComponent", "selector": "fin-document-classification", "modulePath": "@fincloud/ui/document-classification", "exportPath": "@fincloud/ui/document-classification", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/document-classification/src/lib/components/document-classification/document-classification.component.ts", "dependencies": ["@fincloud/ui/animations", "@fincloud/ui/button", "@fincloud/ui/field-message", "@fincloud/ui/input", "@fincloud/ui/truncate-text", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor", "@fincloud/utils/services"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-document-classification", "inputs": [{"name": "expanded", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "expandDisabled", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "readonly", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "loading", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "aiPrediction", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "rootFolderId", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "placeholder", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "canceledLoading", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngOnChanges", "signature": "ngOnChanges(changes: SimpleChanges): void", "parameters": [{"name": "changes", "type": "SimpleChanges", "optional": false}], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "ngOnInit", "signature": "ngOnInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "ngAfterViewInit", "signature": "ngAfterViewInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "onUpdateState", "signature": "onUpdateState(state: Partial<DocumentClassificationState>): any", "parameters": [{"name": "state", "type": "Partial<DocumentClassificationState>", "optional": false}], "returnType": "any", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/animations", "@fincloud/ui/button", "@fincloud/ui/field-message", "@fincloud/ui/input", "@fincloud/ui/truncate-text", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor", "@fincloud/utils/services"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinDocumentClassificationLoadingComponent": {"metadata": {"name": "FinDocumentClassificationLoadingComponent", "selector": "fin-document-classification-loading", "modulePath": "@fincloud/ui/document-classification", "exportPath": "@fincloud/ui/document-classification", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/document-classification/src/lib/components/document-classification-loading/document-classification-loading.component.ts", "dependencies": ["@fincloud/ui/animations", "@fincloud/ui/button", "@fincloud/ui/truncate-text", "@fincloud/ui/types", "@fincloud/utils/functions"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-document-classification-loading", "inputs": [{"name": "state", "type": "DocumentClassificationState", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "controlValue", "type": "string", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "btnCancelLabel", "type": "string", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "max<PERSON><PERSON><PERSON>", "type": "number", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "canceledLoading", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "updateState", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngOnChanges", "signature": "ngOnChanges(changes: SimpleChanges): void", "parameters": [{"name": "changes", "type": "SimpleChanges", "optional": false}], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "ngAfterViewInit", "signature": "ngAfterViewInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/animations", "@fincloud/ui/button", "@fincloud/ui/truncate-text", "@fincloud/ui/types", "@fincloud/utils/functions"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinDropdownComponent": {"metadata": {"name": "FinDropdownComponent", "selector": "fin-dropdown", "modulePath": "@fincloud/ui/dropdown", "exportPath": "@fincloud/ui/dropdown", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/dropdown/src/lib/components/dropdown/dropdown.component.ts", "dependencies": ["@fincloud/ui/ai-suggestion", "@fincloud/ui/avatar-default", "@fincloud/ui/button", "@fincloud/ui/icon", "@fincloud/ui/truncate-text", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor", "@fincloud/utils/pipes", "@fincloud/utils/services", "@fincloud/utils/unpatched-api"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-dropdown", "inputs": [{"name": "label", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "placeholder", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "options", "type": "FinDropdownOption<any>[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "labelPropertyName", "type": "any", "required": true, "defaultValue": "label", "documentation": "", "examples": [], "deprecated": false}, {"name": "valuePropertyName", "type": "any", "required": true, "defaultValue": "value", "documentation": "", "examples": [], "deprecated": false}, {"name": "multiple", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "showChips", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "autocomplete", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "dynamicErrorSpace", "type": "FinErrorSpace", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "readonly", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "messageThreshold", "type": "any", "required": true, "defaultValue": 3, "documentation": "", "examples": [], "deprecated": false}, {"name": "externalFieldMessages", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "hideArrow", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "inputDisplayFn", "type": "(value: any) => string", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "ai<PERSON>nabled", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "selectionChange", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "autoCompleteInputChange", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "chipRemoved", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "aiSuggestionReady", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngOnChanges", "signature": "ngOnChanges(changes: SimpleChanges): void", "parameters": [{"name": "changes", "type": "SimpleChanges", "optional": false}], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "ngAfterViewInit", "signature": "ngAfterViewInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/ai-suggestion", "@fincloud/ui/avatar-default", "@fincloud/ui/button", "@fincloud/ui/icon", "@fincloud/ui/truncate-text", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor", "@fincloud/utils/pipes", "@fincloud/utils/services", "@fincloud/utils/unpatched-api"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinDropdownChipsComponent": {"metadata": {"name": "FinDropdownChipsComponent", "selector": "fin-dropdown-chips", "modulePath": "@fincloud/ui/dropdown", "exportPath": "@fincloud/ui/dropdown", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/dropdown/src/lib/components/dropdown-chips/dropdown-chips.component.ts", "dependencies": ["@fincloud/ui/button", "@fincloud/ui/icon", "@fincloud/ui/truncate-text", "@fincloud/ui/types"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-dropdown-chips", "inputs": [{"name": "options", "type": "FinDropdownOption<any>[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "showClose", "type": "any", "required": true, "defaultValue": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "labelPropertyName", "type": "any", "required": true, "defaultValue": "label", "documentation": "", "examples": [], "deprecated": false}, {"name": "valuePropertyName", "type": "any", "required": true, "defaultValue": "value", "documentation": "", "examples": [], "deprecated": false}, {"name": "chipPrefix", "type": "FinChipPrefixDirective<T>", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "chipSuffix", "type": "FinChipSuffixDirective<T>", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "removeOption", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/button", "@fincloud/ui/icon", "@fincloud/ui/truncate-text", "@fincloud/ui/types"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinDropdownOptionComponent": {"metadata": {"name": "FinDropdownOptionComponent", "selector": "fin-dropdown-option", "modulePath": "@fincloud/ui/dropdown", "exportPath": "@fincloud/ui/dropdown", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/dropdown/src/lib/components/dropdown-option/dropdown-option.component.ts", "dependencies": ["@fincloud/ui/truncate-text"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-dropdown-option", "inputs": [{"name": "option", "type": "FinDropdownOption<any>", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "labelPropertyName", "type": "any", "required": true, "defaultValue": "label", "documentation": "", "examples": [], "deprecated": false}, {"name": "isMatOptionsSelected", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "multiple", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "autocomplete", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "showChips", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "optionSuffix", "type": "FinOptionSuffixDirective<T>", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "optionPrefix", "type": "FinOptionPrefixDirective<T>", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "optionLabel", "type": "FinOptionLabelDirective<T>", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/truncate-text"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinEmptyStateComponent": {"metadata": {"name": "FinEmptyStateComponent", "selector": "fin-empty-state", "modulePath": "@fincloud/ui/empty-state", "exportPath": "@fincloud/ui/empty-state", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/empty-state/src/lib/components/empty-state/empty-state.component.ts", "dependencies": ["@fincloud/ui/icon"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-empty-state", "inputs": [{"name": "type", "type": "FinEmptyStateType", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "title", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinAccordionComponent": {"metadata": {"name": "FinAccordionComponent", "selector": "fin-accordion", "modulePath": "@fincloud/ui/expansion-panel", "exportPath": "@fincloud/ui/expansion-panel", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/expansion-panel/src/lib/components/accordion/accordion.component.ts", "dependencies": ["@fincloud/ui/types", "@fincloud/utils/angular-material"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-accordion", "inputs": [{"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "borderless", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "toggleDirection", "type": "FinAccordionToggleDirection", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "type", "type": "FinAccordionType", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "resetNesting", "type": "boolean", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [{"name": "ngOnInit", "signature": "ngOnInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/types", "@fincloud/utils/angular-material"], "dependents": ["FinBarChartComponent"], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinExpansionPanelComponent": {"metadata": {"name": "FinExpansionPanelComponent", "selector": "fin-expansion-panel", "modulePath": "@fincloud/ui/expansion-panel", "exportPath": "@fincloud/ui/expansion-panel", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/expansion-panel/src/lib/components/expansion-panel/expansion-panel.component.ts", "dependencies": ["@fincloud/ui/types", "@fincloud/utils/angular-material"], "documentation": "", "category": "layout", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-expansion-panel", "inputs": [{"name": "isSummaryAlwaysVisible", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [{"name": "onOpened", "signature": "onOpened(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}, {"name": "onClosed", "signature": "onClosed(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}, {"name": "onAfterExpand", "signature": "onAfterExpand(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}, {"name": "onAfterCollapse", "signature": "onAfterCollapse(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}, {"name": "ngOnInit", "signature": "ngOnInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/types", "@fincloud/utils/angular-material"], "dependents": [], "relatedComponents": ["FinCardLabelComponent", "FinSidePanelComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinFieldMessagesComponent": {"metadata": {"name": "FinFieldMessagesComponent", "selector": "fin-field-messages", "modulePath": "@fincloud/ui/field-message", "exportPath": "@fincloud/ui/field-message", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/field-message/src/lib/components/field-messages/field-messages.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor", "@fincloud/utils/services"], "documentation": "", "category": "form", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-field-messages", "inputs": [], "outputs": [], "methods": [{"name": "ngOnInit", "signature": "ngOnInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "ngAfterContentInit", "signature": "ngAfterContentInit(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor", "@fincloud/utils/services"], "dependents": ["FinDocumentClassificationComponent"], "relatedComponents": ["FinInputDemoComponent", "FinInputComponent", "FinInputCountdownComponent", "FinInputProgressComponent", "FinStepperFieldComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinFileUploadComponent": {"metadata": {"name": "FinFileUploadComponent", "selector": "fin-file-upload", "modulePath": "@fincloud/ui/file-upload", "exportPath": "@fincloud/ui/file-upload", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/file-upload/src/lib/components/file-upload/file-upload.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor", "@fincloud/utils/services"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-file-upload", "inputs": [{"name": "label", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "placeholder", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "dynamicErrorSpace", "type": "FinErrorSpace", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "readonly", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "externalFieldMessages", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "selectFile", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngAfterViewInit", "signature": "ngAfterViewInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor", "@fincloud/utils/services"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinFilterTabsComponent": {"metadata": {"name": "FinFilterTabsComponent", "selector": "fin-filter-tabs", "modulePath": "@fincloud/ui/filter-tabs", "exportPath": "@fincloud/ui/filter-tabs", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/filter-tabs/src/lib/components/filter-tabs.component.ts", "dependencies": ["@fincloud/ui/badges", "@fincloud/utils/angular-material", "@fincloud/utils/control-value-accessor"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-filter-tabs", "inputs": [{"name": "label", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "count", "type": "any", "required": true, "defaultValue": 0, "documentation": "", "examples": [], "deprecated": false}, {"name": "value", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "isSelected", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "selectedTabChange", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngOnInit", "signature": "ngOnInit(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/badges", "@fincloud/utils/angular-material", "@fincloud/utils/control-value-accessor"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinIconComponent": {"metadata": {"name": "FinIconComponent", "selector": "fin-icon", "modulePath": "@fincloud/ui/icon", "exportPath": "@fincloud/ui/icon", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/icon/src/lib/components/icon/icon.component.ts", "dependencies": ["@fincloud/ui/types", "@fincloud/utils/angular-material"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-icon", "inputs": [{"name": "name", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "src", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "matIconOutlined", "type": "any", "required": true, "defaultValue": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [{"name": "ngOnInit", "signature": "ngOnInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/types", "@fincloud/utils/angular-material"], "dependents": ["FinBadgeAppComponent", "FinBadgeIconComponent", "FinBadgeStatusComponent", "FinBreadcrumbComponent", "FinButtonComponent", "FinButtonLinkComponent", "FinCardLabelComponent", "FinDatePickerComponent", "FinDirectoryComponent", "FinDocumentComponent", "FinDropdownComponent", "FinDropdownChipsComponent", "FinEmptyStateComponent", "FinFieldMessagesComponent", "FinFileUploadComponent", "FinInputComponent", "FinMenuItemComponent", "FinBasicUsageComponent", "FinConfirmationComponent", "FinFullSizeComponent", "FinModalConfigsComponent", "FinSlotExampleComponent", "FinCompactPaginatorComponent", "FinPaginatorComponent", "FinSearchComponent", "FinSplitButtonComponent", "FinStepperFieldComponent", "FinSwitchToggleComponent", "FinTableComponent", "FinToastComponent", "FinTreeMenuComponent", "FinTreeNodeComponent", "FinWarningMessageComponent", "FinZoomBarComponent"], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinInputComponent": {"metadata": {"name": "FinInputComponent", "selector": "fin-input", "modulePath": "@fincloud/ui/input", "exportPath": "@fincloud/ui/input", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/input/src/lib/components/input/input.component.ts", "dependencies": ["@fincloud/ui/ai-suggestion", "@fincloud/ui/loader", "@fincloud/utils/control-value-accessor", "@fincloud/utils/services", "@fincloud/ui/icon", "@fincloud/ui/truncate-text", "@fincloud/ui/types"], "documentation": "", "category": "form", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-input", "inputs": [{"name": "label", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "placeholder", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "type", "type": "FinInputType", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "showPasswordButton", "type": "any", "required": true, "defaultValue": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "showProgress", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "readonly", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "ai<PERSON>nabled", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "max<PERSON><PERSON><PERSON>", "type": "any", "required": true, "defaultValue": 0, "documentation": "", "examples": [], "deprecated": false}, {"name": "min", "type": "number", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "max", "type": "number", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "dynamicErrorSpace", "type": "FinErrorSpace", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "externalFieldMessages", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "aiSuggestionReady", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngOnInit", "signature": "ngOnInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "ngOnChanges", "signature": "ngOnChanges(changes: SimpleChanges): void", "parameters": [{"name": "changes", "type": "SimpleChanges", "optional": false}], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "ngAfterViewInit", "signature": "ngAfterViewInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/ai-suggestion", "@fincloud/ui/loader", "@fincloud/utils/control-value-accessor", "@fincloud/utils/services", "@fincloud/ui/icon", "@fincloud/ui/truncate-text", "@fincloud/ui/types"], "dependents": ["FinInputDemoComponent", "FinDocumentClassificationComponent", "FinStepperFieldComponent"], "relatedComponents": ["FinInputDemoComponent", "FinFieldMessagesComponent", "FinInputCountdownComponent", "FinInputProgressComponent", "FinStepperFieldComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinInputCountdownComponent": {"metadata": {"name": "FinInputCountdownComponent", "selector": "fin-input-countdown", "modulePath": "@fincloud/ui/input", "exportPath": "@fincloud/ui/input", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/input/src/lib/components/input-countdown/input-countdown.component.ts", "dependencies": ["@fincloud/utils/services"], "documentation": "", "category": "form", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-input-countdown", "inputs": [], "outputs": [], "methods": [{"name": "ngOnInit", "signature": "ngOnInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/utils/services"], "dependents": [], "relatedComponents": ["FinInputDemoComponent", "FinFieldMessagesComponent", "FinInputComponent", "FinInputProgressComponent", "FinStepperFieldComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinInputProgressComponent": {"metadata": {"name": "FinInputProgressComponent", "selector": "fin-input-progress", "modulePath": "@fincloud/ui/input", "exportPath": "@fincloud/ui/input", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/input/src/lib/components/input-progress/input-progress.component.ts", "dependencies": [], "documentation": "", "category": "form", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-input-progress", "inputs": [{"name": "progress", "type": "any", "required": true, "defaultValue": 0, "documentation": "", "examples": [], "deprecated": false}, {"name": "successPercentage", "type": "any", "required": true, "defaultValue": 50, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": [], "dependents": [], "relatedComponents": ["FinInputDemoComponent", "FinFieldMessagesComponent", "FinInputComponent", "FinInputCountdownComponent", "FinStepperFieldComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinLoaderComponent": {"metadata": {"name": "FinLoaderComponent", "selector": "fin-loader", "modulePath": "@fincloud/ui/loader", "exportPath": "@fincloud/ui/loader", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/loader/src/lib/components/loader/loader.component.ts", "dependencies": [], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-loader", "inputs": [{"name": "hide", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": [], "dependents": ["FinDocumentComponent", "FinInputComponent", "FinSearchComponent", "FinTextAreaComponent"], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinMenuItemComponent": {"metadata": {"name": "FinMenuItemComponent", "selector": "", "modulePath": "@fincloud/ui/menu-item", "exportPath": "@fincloud/ui/menu-item", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/menu-item/src/lib/components/menu-item/menu-item.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types"], "documentation": "", "category": "navigation", "tags": [], "deprecated": false}, "api": {"selector": "", "inputs": [{"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "iconName", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "iconSrc", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "compact", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "attention", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "active", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types"], "dependents": ["FinBreadcrumbComponent", "FinSplitButtonComponent"], "relatedComponents": ["FinActionsMenuComponent", "FinAvatarDefaultComponent", "FinAvatarParticipantsComponent", "FinBreadcrumbComponent", "FinTreeMenuComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinModalSlotsContainerComponent": {"metadata": {"name": "FinModalSlotsContainerComponent", "selector": "fin-modal-slots-container", "modulePath": "@fincloud/ui/modal", "exportPath": "@fincloud/ui/modal", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/modal/src/lib/components/fin-modal-slots-container/fin-modal-slots-container.component.ts", "dependencies": [], "documentation": "", "category": "feedback", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-modal-slots-container", "inputs": [], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": [], "dependents": [], "relatedComponents": ["FinModalConfigsComponent", "FinToastComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinBasicUsageComponent": {"metadata": {"name": "FinBasicUsageComponent", "selector": "fin-basic-usage", "modulePath": "@fincloud/ui/modal", "exportPath": "@fincloud/ui/modal", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/modal/src/lib/examples/basic-usage/basic-usage.component.ts", "dependencies": ["@fincloud/ui/button", "@fincloud/ui/header-and-footer", "@fincloud/ui/icon", "@fincloud/ui/types"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-basic-usage", "inputs": [], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/button", "@fincloud/ui/header-and-footer", "@fincloud/ui/icon", "@fincloud/ui/types"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinConfirmationComponent": {"metadata": {"name": "FinConfirmationComponent", "selector": "fin-confirmation", "modulePath": "@fincloud/ui/modal", "exportPath": "@fincloud/ui/modal", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/modal/src/lib/examples/confirmation/confirmation.component.ts", "dependencies": ["@fincloud/ui/button", "@fincloud/ui/header-and-footer", "@fincloud/ui/icon"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-confirmation", "inputs": [], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/button", "@fincloud/ui/header-and-footer", "@fincloud/ui/icon"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinFullSizeComponent": {"metadata": {"name": "FinFullSizeComponent", "selector": "fin-full-size", "modulePath": "@fincloud/ui/modal", "exportPath": "@fincloud/ui/modal", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/modal/src/lib/examples/full-size/full-size.component.ts", "dependencies": ["@fincloud/ui/button", "@fincloud/ui/header-and-footer", "@fincloud/ui/icon", "@fincloud/ui/types"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-full-size", "inputs": [], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/button", "@fincloud/ui/header-and-footer", "@fincloud/ui/icon", "@fincloud/ui/types"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinLayoutComponent": {"metadata": {"name": "FinLayoutComponent", "selector": "fin-layout", "modulePath": "@fincloud/ui/modal", "exportPath": "@fincloud/ui/modal", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/modal/src/lib/examples/layout/layout.component.ts", "dependencies": ["@fincloud/ui/button", "@fincloud/ui/types"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-layout", "inputs": [{"name": "exampleNumber", "type": "any", "required": true, "defaultValue": 1, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [{"name": "openModal", "signature": "openModal(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/button", "@fincloud/ui/types"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinModalConfigsComponent": {"metadata": {"name": "FinModalConfigsComponent", "selector": "fin-modal-configs", "modulePath": "@fincloud/ui/modal", "exportPath": "@fincloud/ui/modal", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/modal/src/lib/examples/modal-configs/modal-configs.component.ts", "dependencies": ["@fincloud/ui/button", "@fincloud/ui/header-and-footer", "@fincloud/ui/icon", "@fincloud/ui/types"], "documentation": "", "category": "feedback", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-modal-configs", "inputs": [], "outputs": [], "methods": [{"name": "ngOnInit", "signature": "ngOnInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/button", "@fincloud/ui/header-and-footer", "@fincloud/ui/icon", "@fincloud/ui/types"], "dependents": [], "relatedComponents": ["FinModalSlotsContainerComponent", "FinToastComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinSlotExampleComponent": {"metadata": {"name": "FinSlotExampleComponent", "selector": "fin-slot-example", "modulePath": "@fincloud/ui/modal", "exportPath": "@fincloud/ui/modal", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/modal/src/lib/examples/slot-example/slot-example.component.ts", "dependencies": ["@fincloud/ui/button", "@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/ui/header-and-footer"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-slot-example", "inputs": [], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/button", "@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/ui/header-and-footer"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinCompactPaginatorComponent": {"metadata": {"name": "FinCompactPaginatorComponent", "selector": "fin-compact-paginator", "modulePath": "@fincloud/ui/paginator", "exportPath": "@fincloud/ui/paginator", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/paginator/src/lib/components/compact-paginator/compact-paginator.component.ts", "dependencies": ["@fincloud/ui/types", "@fincloud/ui/icon", "@fincloud/ui/button"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-compact-paginator", "inputs": [{"name": "totalItems", "type": "any", "required": true, "defaultValue": 0, "documentation": "", "examples": [], "deprecated": false}, {"name": "pageSize", "type": "any", "required": true, "defaultValue": 1, "documentation": "", "examples": [], "deprecated": false}, {"name": "pageNumber", "type": "any", "required": true, "defaultValue": 1, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "pageChange", "type": "EventEmitter<FinPaginatorEvent>", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngOnChanges", "signature": "ngOnChanges(changes: SimpleChanges): void", "parameters": [{"name": "changes", "type": "SimpleChanges", "optional": false}], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/types", "@fincloud/ui/icon", "@fincloud/ui/button"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinPaginatorComponent": {"metadata": {"name": "FinPaginatorComponent", "selector": "fin-paginator", "modulePath": "@fincloud/ui/paginator", "exportPath": "@fincloud/ui/paginator", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/paginator/src/lib/components/paginator/paginator.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-paginator", "inputs": [{"name": "totalItems", "type": "any", "required": true, "defaultValue": 0, "documentation": "", "examples": [], "deprecated": false}, {"name": "pageSize", "type": "any", "required": true, "defaultValue": 10, "documentation": "", "examples": [], "deprecated": false}, {"name": "pageNumber", "type": "any", "required": true, "defaultValue": 1, "documentation": "", "examples": [], "deprecated": false}, {"name": "pageSizeOptions", "type": "number[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "showPageSize", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "showGoToPage", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "pageChange", "type": "EventEmitter<FinPaginatorEvent>", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngOnChanges", "signature": "ngOnChanges(changes: SimpleChanges): void", "parameters": [{"name": "changes", "type": "SimpleChanges", "optional": false}], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinPercentageLoaderComponent": {"metadata": {"name": "FinPercentageLoaderComponent", "selector": "fin-percentage-loader", "modulePath": "@fincloud/ui/percentage-loader", "exportPath": "@fincloud/ui/percentage-loader", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/percentage-loader/src/lib/components/percentage-loader/percentage-loader.component.ts", "dependencies": ["@fincloud/utils/pipes"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-percentage-loader", "inputs": [], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/utils/pipes"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinProgressBarComponent": {"metadata": {"name": "FinProgressBarComponent", "selector": "fin-progress-bar", "modulePath": "@fincloud/ui/progress-bar", "exportPath": "@fincloud/ui/progress-bar", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/progress-bar/src/lib/components/progress-bar.component.ts", "dependencies": ["@fincloud/ui/tooltip"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-progress-bar", "inputs": [{"name": "segments", "type": "FinProgressBarOption[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "min", "type": "number", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "max", "type": "number", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "amountSuffix", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "baseColor", "type": "string", "required": true, "defaultValue": "fin-bg-color-background-tertiary-minimal", "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "selectedIndex", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/tooltip"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinRadioComponent": {"metadata": {"name": "FinRadioComponent", "selector": "fin-radio-button", "modulePath": "@fincloud/ui/radio", "exportPath": "@fincloud/ui/radio", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/radio/src/lib/components/radio.component.ts", "dependencies": ["@fincloud/ui/types", "@fincloud/utils/angular-material", "@fincloud/utils/control-value-accessor"], "documentation": "", "category": "action", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-radio-button", "inputs": [{"name": "options", "type": "FinRadioOption[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "labelPropertyName", "type": "any", "required": true, "defaultValue": "label", "documentation": "", "examples": [], "deprecated": false}, {"name": "valuePropertyName", "type": "any", "required": true, "defaultValue": "value", "documentation": "", "examples": [], "deprecated": false}, {"name": "value", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "label", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "labelPosition", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "standalone", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "selectionChanged", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/types", "@fincloud/utils/angular-material", "@fincloud/utils/control-value-accessor"], "dependents": [], "relatedComponents": ["FinButtonComponent", "FinButtonActionComponent", "FinButtonFabComponent", "FinButtonLinkComponent", "FinSplitButtonComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinRangeSliderComponent": {"metadata": {"name": "FinRangeSliderComponent", "selector": "fin-range-slider", "modulePath": "@fincloud/ui/range-slider", "exportPath": "@fincloud/ui/range-slider", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/range-slider/src/lib/components/range-slider/range-slider.component.ts", "dependencies": ["@fincloud/utils/pipes"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-range-slider", "inputs": [{"name": "startThumbControl", "type": "FormControl<number>", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "endThumbControl", "type": "FormControl<number>", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "min", "type": "any", "required": true, "defaultValue": 0, "documentation": "", "examples": [], "deprecated": false}, {"name": "max", "type": "any", "required": true, "defaultValue": 100, "documentation": "", "examples": [], "deprecated": false}, {"name": "step", "type": "any", "required": true, "defaultValue": 1, "documentation": "", "examples": [], "deprecated": false}, {"name": "disabled", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "showTooltip", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "tooltipAlwaysVisible", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "displayWith", "type": "(value: number) => string", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "startThumbDragStart", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "startThumbDragEnd", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "endThumbDragStart", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "endThumbDragEnd", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/utils/pipes"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinScrollbarComponent": {"metadata": {"name": "FinScrollbarComponent", "selector": "fin-scrollbar", "modulePath": "@fincloud/ui/scrollbar", "exportPath": "@fincloud/ui/scrollbar", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/scrollbar/src/lib/components/scrollbar/scrollbar.component.ts", "dependencies": ["@fincloud/ui/observers"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-scrollbar", "inputs": [{"name": "reachedTopOffset", "type": "any", "required": true, "defaultValue": 50, "documentation": "", "examples": [], "deprecated": false}, {"name": "reachedBottomOffset", "type": "any", "required": true, "defaultValue": 50, "documentation": "", "examples": [], "deprecated": false}, {"name": "enableInfinityScroll", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "alwaysVisible", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "disabled", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "smoothResize", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "smoothResizeDuration", "type": "any", "required": true, "defaultValue": 400, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "reachedTop", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "reachedBottom", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "afterInit", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "afterUpdate", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "update", "signature": "update(reason?: FinScrollbarUpdateReason): any", "parameters": [{"name": "reason", "type": "FinScrollbarUpdateReason", "optional": true}], "returnType": "any", "documentation": "", "isPublic": true}, {"name": "scrollTo", "signature": "scrollTo(options: FinSmoothScrollToOptions): Promise<void>", "parameters": [{"name": "options", "type": "FinSmoothScrollToOptions", "optional": false}], "returnType": "Promise<void>", "documentation": "", "isPublic": true}, {"name": "scrollToElement", "signature": "scrollToElement(target: SmoothScrollElement, options: SmoothScrollToElementOptions): Promise<void>", "parameters": [{"name": "target", "type": "SmoothScrollElement", "optional": false}, {"name": "options", "type": "SmoothScrollToElementOptions", "optional": false}], "returnType": "Promise<void>", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/observers"], "dependents": ["FinTextAreaComponent"], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinSearchComponent": {"metadata": {"name": "FinSearchComponent", "selector": "fin-search", "modulePath": "@fincloud/ui/search", "exportPath": "@fincloud/ui/search", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/search/src/lib/components/search/search.component.ts", "dependencies": ["@fincloud/ui/badges", "@fincloud/ui/icon", "@fincloud/ui/loader", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-search", "inputs": [{"name": "placeholder", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "numberOfResults", "type": "number", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "autocomplete", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "options", "type": "FinSearchAutocompleteOption[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "labelPropertyName", "type": "any", "required": true, "defaultValue": "label", "documentation": "", "examples": [], "deprecated": false}, {"name": "valuePropertyName", "type": "any", "required": true, "defaultValue": "value", "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "dynamicErrorSpace", "type": "FinErrorSpace", "required": true, "defaultValue": "dynamic", "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "inputChange", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "inputFocus", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "autoCompleteOptionChange", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "pressEnter", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/badges", "@fincloud/ui/icon", "@fincloud/ui/loader", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinSidePanelComponent": {"metadata": {"name": "FinSidePanelComponent", "selector": "fin-side-panel", "modulePath": "@fincloud/ui/side-panel", "exportPath": "@fincloud/ui/side-panel", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/side-panel/src/lib/components/side-panel/side-panel.component.ts", "dependencies": ["@fincloud/utils/functions"], "documentation": "", "category": "layout", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-side-panel", "inputs": [{"name": "open", "type": "any", "required": true, "defaultValue": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "partiallyOpenSize", "type": "any", "required": true, "defaultValue": 0, "documentation": "", "examples": [], "deprecated": false}, {"name": "mode", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "position", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "elevation", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "hasBackdrop", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "transparentBackdrop", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "disableClose", "type": "any", "required": true, "defaultValue": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "fixedInViewport", "type": "any", "required": true, "defaultValue": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "fixSticky", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "isOpen", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "toggleSidePanel", "signature": "toggleSidePanel(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/utils/functions"], "dependents": [], "relatedComponents": ["FinCardLabelComponent", "FinExpansionPanelComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinSlideToggleComponent": {"metadata": {"name": "FinSlideToggleComponent", "selector": "fin-slide-toggle", "modulePath": "@fincloud/ui/slide-toggle", "exportPath": "@fincloud/ui/slide-toggle", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/slide-toggle/src/lib/components/slide-toggle.component.ts", "dependencies": ["@fincloud/ui/types", "@fincloud/utils/angular-material", "@fincloud/utils/control-value-accessor"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-slide-toggle", "inputs": [{"name": "label", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "labelPosition", "type": "FinSlideTogglePosition", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "type", "type": "FinSlideToggleType", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "isManuallyUpdated", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "class", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "checked", "type": "boolean", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "disabled", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "slideChange", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/types", "@fincloud/utils/angular-material", "@fincloud/utils/control-value-accessor"], "dependents": ["FinInputDemoComponent"], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinSplitButtonComponent": {"metadata": {"name": "FinSplitButtonComponent", "selector": "fin-split-button", "modulePath": "@fincloud/ui/split-button", "exportPath": "@fincloud/ui/split-button", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/split-button/src/lib/components/split-button.component.ts", "dependencies": ["@fincloud/ui/actions-menu", "@fincloud/ui/icon", "@fincloud/ui/menu-item", "@fincloud/ui/types", "@fincloud/utils/angular-material"], "documentation": "", "category": "action", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-split-button", "inputs": [{"name": "label", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "type", "type": "FinSplitButtonType", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "disabled", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "options", "type": "FinSplitButtonOption[]", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "buttonClick", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "optionClick", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/actions-menu", "@fincloud/ui/icon", "@fincloud/ui/menu-item", "@fincloud/ui/types", "@fincloud/utils/angular-material"], "dependents": [], "relatedComponents": ["FinButtonComponent", "FinButtonActionComponent", "FinButtonFabComponent", "FinButtonLinkComponent", "FinRadioComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinStepperFieldComponent": {"metadata": {"name": "FinStepperFieldComponent", "selector": "fin-stepper-field", "modulePath": "@fincloud/ui/stepper-field", "exportPath": "@fincloud/ui/stepper-field", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/stepper-field/src/lib/components/stepper-field/stepper-field.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/input", "@fincloud/ui/types", "@fincloud/utils/angular-material", "@fincloud/utils/control-value-accessor", "@fincloud/utils/services"], "documentation": "", "category": "form", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-stepper-field", "inputs": [{"name": "label", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "min", "type": "number", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "max", "type": "number", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "externalFieldMessages", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "readonly", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "inputChange", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngAfterViewInit", "signature": "ngAfterViewInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/input", "@fincloud/ui/types", "@fincloud/utils/angular-material", "@fincloud/utils/control-value-accessor", "@fincloud/utils/services"], "dependents": [], "relatedComponents": ["FinInputDemoComponent", "FinFieldMessagesComponent", "FinInputComponent", "FinInputCountdownComponent", "FinInputProgressComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinSwitchToggleComponent": {"metadata": {"name": "FinSwitchToggleComponent", "selector": "fin-switch-toggle", "modulePath": "@fincloud/ui/switch-toggle", "exportPath": "@fincloud/ui/switch-toggle", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/switch-toggle/src/lib/components/switch-toggle/switch-toggle.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/angular-material", "@fincloud/utils/control-value-accessor", "@fincloud/ui/tooltip"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-switch-toggle", "inputs": [{"name": "options", "type": "FinSwitchToggleOption[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "stretched", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "switchChange", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/angular-material", "@fincloud/utils/control-value-accessor", "@fincloud/ui/tooltip"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinTableComponent": {"metadata": {"name": "FinTableComponent", "selector": "fin-table", "modulePath": "@fincloud/ui/table", "exportPath": "@fincloud/ui/table", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/table/src/lib/components/table/table.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/observers", "@fincloud/ui/types", "@fincloud/utils/angular-functions", "@fincloud/utils/functions", "@fincloud/utils/pipes"], "documentation": "", "category": "data-display", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-table", "inputs": [{"name": "autoHideColumns", "type": "any", "required": true, "defaultValue": false, "documentation": "*", "examples": [], "deprecated": false}, {"name": "rows", "type": "T[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "columns", "type": "FinTableColumn[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "columnMode", "type": "FinTableColumnMode", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "headerHeight", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "rowHeight", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "useDefaultSort", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "initialSort", "type": "FinTableSort", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "hasBorderRadius", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "isHeaderBgTransparent", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "headerClasses", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "hasRowBorder", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "hasRowPartialBorder", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "hasRowSpacing", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "scrollbarH", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "scrollbarV", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "resizeable", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "emptyMessage", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "<PERSON><PERSON>ead<PERSON>", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "expandGroups", "type": "any", "required": true, "defaultValue": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "groupRowsBy", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "multiExpandDetails", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "columnsToHide", "type": "string[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "canExpand", "type": "(row: T) => boolean", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "rowClassFn", "type": "(row: T, index: number) => string", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "sortChange", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "rowClick", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngAfterViewInit", "signature": "ngAfterViewInit(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}, {"name": "ngAfterContentInit", "signature": "ngAfterContentInit(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}, {"name": "ngOnChanges", "signature": "ngOnChanges(changes: SimpleChanges): any", "parameters": [{"name": "changes", "type": "SimpleChanges", "optional": false}], "returnType": "any", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/observers", "@fincloud/ui/types", "@fincloud/utils/angular-functions", "@fincloud/utils/functions", "@fincloud/utils/pipes"], "dependents": [], "relatedComponents": ["FinAreaChartComponent", "FinBarChartComponent", "FinDoughnutChartComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinTabComponent": {"metadata": {"name": "FinTabComponent", "selector": "fin-tab", "modulePath": "@fincloud/ui/tabs", "exportPath": "@fincloud/ui/tabs", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/tabs/src/lib/components/tab/tab.component.ts", "dependencies": [], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-tab", "inputs": [{"name": "disabled", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "isHidden", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [{"name": "ngAfterContentInit", "signature": "ngAfterContentInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": [], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinTabsComponent": {"metadata": {"name": "FinTabsComponent", "selector": "fin-tabs", "modulePath": "@fincloud/ui/tabs", "exportPath": "@fincloud/ui/tabs", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/tabs/src/lib/components/tabs/tabs.component.ts", "dependencies": ["@fincloud/ui/badges", "@fincloud/ui/types", "@fincloud/utils/angular-functions", "@fincloud/utils/rxjs-operators"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-tabs", "inputs": [{"name": "type", "type": "FinTabType", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "selectedIndex", "type": "any", "required": true, "defaultValue": 0, "documentation": "", "examples": [], "deprecated": false}, {"name": "stretchTabs", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "dynamicIndex", "type": "any", "required": true, "defaultValue": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "focusChange", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "selectedIndexChange", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "selectedTabChange", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngAfterViewInit", "signature": "ngAfterViewInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/badges", "@fincloud/ui/types", "@fincloud/utils/angular-functions", "@fincloud/utils/rxjs-operators"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinTextAreaComponent": {"metadata": {"name": "FinTextAreaComponent", "selector": "fin-text-area", "modulePath": "@fincloud/ui/text-area", "exportPath": "@fincloud/ui/text-area", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/text-area/src/lib/components/text-area/text-area.component.ts", "dependencies": ["@fincloud/ui/ai-suggestion", "@fincloud/ui/loader", "@fincloud/ui/scrollbar", "@fincloud/ui/truncate-text", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor", "@fincloud/utils/functions", "@fincloud/utils/services"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-text-area", "inputs": [{"name": "label", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "placeholder", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "size", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "rows", "type": "any", "required": true, "defaultValue": 5, "documentation": "", "examples": [], "deprecated": false}, {"name": "max<PERSON><PERSON><PERSON>", "type": "any", "required": true, "defaultValue": 0, "documentation": "", "examples": [], "deprecated": false}, {"name": "dynamicErrorSpace", "type": "FinErrorSpace", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "readonly", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "externalFieldMessages", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "autoResize", "type": "any", "required": true, "defaultValue": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "ai<PERSON>nabled", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "enterPressedDown", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "aiSuggestionReady", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngOnInit", "signature": "ngOnInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "ngAfterViewInit", "signature": "ngAfterViewInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/ai-suggestion", "@fincloud/ui/loader", "@fincloud/ui/scrollbar", "@fincloud/ui/truncate-text", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor", "@fincloud/utils/functions", "@fincloud/utils/services"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinToastComponent": {"metadata": {"name": "FinToastComponent", "selector": "fin-toast", "modulePath": "@fincloud/ui/toast", "exportPath": "@fincloud/ui/toast", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/toast/src/lib/components/toast.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/angular-material"], "documentation": "", "category": "feedback", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-toast", "inputs": [], "outputs": [], "methods": [{"name": "ngOnInit", "signature": "ngOnInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/angular-material"], "dependents": [], "relatedComponents": ["FinModalSlotsContainerComponent", "FinModalConfigsComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinBasicComponent": {"metadata": {"name": "FinBasicComponent", "selector": "fin-basic", "modulePath": "@fincloud/ui/toast", "exportPath": "@fincloud/ui/toast", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/toast/src/lib/examples/basic/basic.component.ts", "dependencies": ["@fincloud/ui/button"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-basic", "inputs": [{"name": "toast", "type": "FinToast", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [{"name": "openToast", "signature": "openToast(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/button"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinToolbarComponent": {"metadata": {"name": "FinToolbarComponent", "selector": "fin-toolbar", "modulePath": "@fincloud/ui/toolbar", "exportPath": "@fincloud/ui/toolbar", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/toolbar/src/lib/components/toolbar/toolbar.component.ts", "dependencies": [], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-toolbar", "inputs": [], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": [], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinTooltipComponent": {"metadata": {"name": "FinTooltipComponent", "selector": "fin-tooltip", "modulePath": "@fincloud/ui/tooltip", "exportPath": "@fincloud/ui/tooltip", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/tooltip/src/lib/components/tooltip/tooltip.component.ts", "dependencies": [], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-tooltip", "inputs": [{"name": "content", "type": "string | TemplateRef<unknown>", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "context", "type": "EmbeddedViewRef<unknown>", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "showArrow", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "position", "type": "\"top\" | \"bottom\" | \"left\" | \"right\"", "required": true, "defaultValue": "top", "documentation": "", "examples": [], "deprecated": false}, {"name": "max<PERSON><PERSON><PERSON>", "type": "number", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [{"name": "isTemplate", "signature": "isTemplate(value: string | TemplateRef<unknown>): boolean", "parameters": [{"name": "value", "type": "string | TemplateRef<unknown>", "optional": false}], "returnType": "boolean", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": [], "dependents": ["FinBreadcrumbComponent", "FinProgressBarComponent", "FinSwitchToggleComponent"], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinTreeMenuComponent": {"metadata": {"name": "FinTreeMenuComponent", "selector": "fin-tree-menu", "modulePath": "@fincloud/ui/tree-menu", "exportPath": "@fincloud/ui/tree-menu", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/tree-menu/src/lib/components/tree-menu/tree-menu.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types"], "documentation": "", "category": "navigation", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-tree-menu", "inputs": [{"name": "data", "type": "FinTreeNode<T>[]", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "appearance", "type": "FinTreeNodeAppearance", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "expandOnDrop", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "alwaysExpand", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "finDropListDropped", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "finDropListExited", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "finDropListEntered", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "finTreeNodeToggle", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngAfterViewInit", "signature": "ngAfterViewInit(): any", "parameters": [], "returnType": "any", "documentation": "", "isPublic": true}, {"name": "ngOnChanges", "signature": "ngOnChanges(changes: SimpleChanges): any", "parameters": [{"name": "changes", "type": "SimpleChanges", "optional": false}], "returnType": "any", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types"], "dependents": [], "relatedComponents": ["FinActionsMenuComponent", "FinAvatarDefaultComponent", "FinAvatarParticipantsComponent", "FinBreadcrumbComponent", "FinMenuItemComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinTreeNodeComponent": {"metadata": {"name": "FinTreeNodeComponent", "selector": "fin-tree-node", "modulePath": "@fincloud/ui/tree-menu", "exportPath": "@fincloud/ui/tree-menu", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/tree-menu/src/lib/components/tree-node/tree-node.component.ts", "dependencies": ["@fincloud/ui/drag-drop", "@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/functions"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-tree-node", "inputs": [{"name": "node", "type": "T", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "expandable", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "expanded", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "active", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "appearance", "type": "FinTreeNodeAppearance", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "dragHandle", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "hoverHighlight", "type": "any", "required": true, "defaultValue": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "nodeSeparator", "type": "any", "required": true, "defaultValue": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "baseOffset", "type": "any", "required": true, "defaultValue": 16, "documentation": "", "examples": [], "deprecated": false}, {"name": "levelOffset", "type": "any", "required": true, "defaultValue": 4, "documentation": "", "examples": [], "deprecated": false}, {"name": "nodeClick", "type": "(event: <PERSON><PERSON><PERSON>, node: T) => void", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "hideExpandButton", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "finTreeNodeToggle", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "finDraggableChange", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [{"name": "ngOnChanges", "signature": "ngOnChanges(changes: SimpleChanges): void", "parameters": [{"name": "changes", "type": "SimpleChanges", "optional": false}], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/drag-drop", "@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/functions"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinTreeNodeCounterComponent": {"metadata": {"name": "FinTreeNodeCounterComponent", "selector": "fin-tree-node-counter", "modulePath": "@fincloud/ui/tree-menu", "exportPath": "@fincloud/ui/tree-menu", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/tree-menu/src/lib/components/tree-node-counter/tree-node-counter.component.ts", "dependencies": [], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-tree-node-counter", "inputs": [{"name": "node", "type": "T", "required": true, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": [], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinWarningMessageComponent": {"metadata": {"name": "FinWarningMessageComponent", "selector": "fin-warning-message", "modulePath": "@fincloud/ui/warning-message", "exportPath": "@fincloud/ui/warning-message", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/warning-message/src/lib/components/warning-message.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/angular-material"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-warning-message", "inputs": [{"name": "label", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "showIcon", "type": "any", "required": true, "defaultValue": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "iconName", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "iconSrc", "type": "any", "required": true, "defaultValue": "", "documentation": "", "examples": [], "deprecated": false}, {"name": "appearance", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "showElevation", "type": "any", "required": true, "defaultValue": false, "documentation": "", "examples": [], "deprecated": false}], "outputs": [], "methods": [{"name": "ngOnInit", "signature": "ngOnInit(): void", "parameters": [], "returnType": "void", "documentation": "", "isPublic": true}], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/angular-material"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}, "FinZoomBarComponent": {"metadata": {"name": "FinZoomBarComponent", "selector": "fin-zoom-bar", "modulePath": "@fincloud/ui/zoom-bar", "exportPath": "@fincloud/ui/zoom-bar", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/ui/zoom-bar/src/lib/components/zoom-bar/zoom-bar.component.ts", "dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor", "@fincloud/utils/functions"], "documentation": "", "category": "other", "tags": ["fincloud"], "deprecated": false}, "api": {"selector": "fin-zoom-bar", "inputs": [{"name": "orientation", "type": "FinZoomBarOrientation", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "min", "type": "any", "required": true, "defaultValue": 0, "documentation": "", "examples": [], "deprecated": false}, {"name": "max", "type": "any", "required": true, "defaultValue": 100, "documentation": "", "examples": [], "deprecated": false}, {"name": "step", "type": "any", "required": true, "defaultValue": 1, "documentation": "", "examples": [], "deprecated": false}, {"name": "zoomBar<PERSON>ength", "type": "any", "required": true, "defaultValue": 160, "documentation": "", "examples": [], "deprecated": false}], "outputs": [{"name": "dragStart", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}, {"name": "dragEnd", "type": "any", "required": false, "documentation": "", "examples": [], "deprecated": false}], "methods": [], "contentProjection": [], "hostBindings": []}, "relationships": {"dependencies": ["@fincloud/ui/icon", "@fincloud/ui/types", "@fincloud/utils/control-value-accessor", "@fincloud/utils/functions"], "dependents": [], "relatedComponents": ["FinAIShadowEffectComponent", "FinAiSuggestionComponent", "FinBorderSweepComponent", "FinBadgeAppComponent", "FinBadgeIconComponent"], "alternatives": [], "commonlyUsedWith": []}, "examples": [], "tests": []}}, "modules": {}, "assets": {}, "styles": {}}, "utils": {"functions": {"detectChangesOnce": {"signature": {"name": "detectChangesOnce", "signature": "detectChangesOnce(cdr: ChangeDetectorRef): void", "parameters": [{"name": "cdr", "type": "ChangeDetectorRef", "optional": false, "documentation": "- The Angular ChangeDetectorRef instance used to control change detection."}], "returnType": "void", "documentation": "- The Angular ChangeDetectorRef instance used to control change detection."}, "usage": [], "relatedFunctions": [], "category": "angular", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/utils/angular-functions/src/lib/detect-changes-once.ts", "exportPath": "@fincloud/utils/angular-functions"}, "createBlockScrollStrategyFactory": {"signature": {"name": "createBlockScrollStrategyFactory", "signature": "createBlockScrollStrategyFactory(overlay: Overlay): () => ScrollStrategy", "parameters": [{"name": "overlay", "type": "Overlay", "optional": false, "documentation": "Overlay service"}], "returnType": "() => ScrollStrategy", "documentation": "Overlay service\nBlockScrollStrategy"}, "usage": [], "relatedFunctions": [], "category": "other", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/utils/angular-material/src/lib/utils/fin-scroll-strategy-block-factory.ts", "exportPath": "@fincloud/utils/angular-material"}, "IgnoresNull": {"signature": {"name": "IgnoresNull", "signature": "IgnoresNull(): (target: T, key: string, descriptor: PropertyDescriptor) => void", "parameters": [], "returnType": "(target: T, key: string, descriptor: PropertyDescriptor) => void", "documentation": "", "typeParameters": ["T"]}, "usage": [], "relatedFunctions": [], "category": "decorators", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/utils/decorators/src/lib/ignores-null.ts", "exportPath": "@fincloud/utils/decorators"}, "pxToRem": {"signature": {"name": "pxToRem", "signature": "pxToRem(pxValue: number): string", "parameters": [{"name": "pxValue", "type": "number", "optional": false}], "returnType": "string", "documentation": ""}, "usage": [], "relatedFunctions": [], "category": "utility", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/utils/functions/src/lib/px-to-rem.ts", "exportPath": "@fincloud/utils/functions"}, "emitPerJsFrame": {"signature": {"name": "emitPerJsFrame", "signature": "emitPerJsFrame(): UnaryFunction<Observable<T>, Observable<T>>", "parameters": [], "returnType": "UnaryFunction<Observable<T>, Observable<T>>", "documentation": "The type of items in the observable sequence.\nAn RxJS unary function (operator) that can be used\nwith the `pipe` method of an Observable to incorporate the frame-based emission logic.\n// Usage with an RxJS Observable:\nimport { fromEvent } from 'rxjs';\nimport { emitPerJsFrame } from '@fincloud/utils/rxjs-operators';\n\nconst mouseMove$ = fromEvent(document, 'mousemove').pipe(\n  emitPerJsFrame()\n);\n\nmouseMove$.subscribe(event => {\n  console.log(event); // Logs the MouseEvent object at most once per JavaScript frame\n});", "typeParameters": ["T"]}, "usage": [], "relatedFunctions": [], "category": "rxjs", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/utils/rxjs-operators/src/lib/emit-per-js-frame.ts", "exportPath": "@fincloud/utils/rxjs-operators"}, "getZoneUnPatchedApi": {"signature": {"name": "getZoneUnPatchedApi", "signature": "getZoneUnPatchedApi(name: keyof Window): any", "parameters": [{"name": "name", "type": "keyof Window", "optional": false}], "returnType": "any", "documentation": ""}, "usage": [], "relatedFunctions": [], "category": "other", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/utils/unpatched-api/src/utils/get-zone-unpatched-api.ts", "exportPath": "@fincloud/utils/unpatched-api"}}, "services": {"FinFieldService": {"className": "FinFieldService", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/utils/services/src/lib/services/field.service.ts", "exportPath": "@fincloud/utils/services", "methods": [{"name": "isInMaxLength", "signature": "isInMaxLength(value: string): boolean", "parameters": [{"name": "value", "type": "string", "optional": false}], "returnType": "boolean", "documentation": "", "isPublic": true}, {"name": "trimByMaxLength", "signature": "trimByMaxLength(value: string): string", "parameters": [{"name": "value", "type": "string", "optional": false}], "returnType": "string", "documentation": "", "isPublic": true}, {"name": "setErrorPriority", "signature": "setErrorPriority(error: string, priority: number): void", "parameters": [{"name": "error", "type": "string", "optional": false}, {"name": "priority", "type": "number", "optional": false}], "returnType": "void", "documentation": "", "isPublic": true}, {"name": "getError", "signature": "getError(): Observable<string>", "parameters": [], "returnType": "Observable<string>", "documentation": "", "isPublic": true}], "properties": [{"name": "value$", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}, {"name": "controlErrors$", "type": "any", "required": true, "documentation": "", "examples": [], "deprecated": false}], "documentation": ""}, "FinIconRegistryService": {"className": "FinIconRegistryService", "filePath": "/Users/<USER>/Projects/lib-ui-2/libs/utils/services/src/lib/services/icon-registry.service.ts", "exportPath": "@fincloud/utils/services", "methods": [{"name": "getSvgIcon", "signature": "getSvgIcon(svgIconName: string, iconPath: string): any", "parameters": [{"name": "svgIconName", "type": "string", "optional": false}, {"name": "iconPath", "type": "string", "optional": false}], "returnType": "any", "documentation": "", "isPublic": true}], "properties": [], "documentation": ""}}, "pipes": {}, "decorators": {}, "operators": {}}}}